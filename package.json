{"name": "live2d-desktop-pet", "private": true, "version": "1.0.0", "description": "Live2D 桌面模型应用", "main": "electron/main.cjs", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "node start-electron.js", "electron:dev": "node start-electron-dev.js", "electron:build": "pnpm build && electron-builder", "electron:dist": "pnpm build && electron-builder --publish=never", "pet": "node start-electron.js", "pet:dev": "npm run dev & sleep 3 && npm run pet"}, "dependencies": {"@iconify-icons/fluent": "^1.2.38", "@iconify-icons/ph": "^1.2.5", "@iconify-icons/tabler": "^1.2.95", "@iconify/vue": "^5.0.0", "electron-updater": "^6.6.2", "pixi-live2d-display": "^0.4.0", "pixi.js": "^6.5.0", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.0", "concurrently": "^9.2.0", "electron": "^37.2.5", "electron-builder": "^26.0.12", "vite": "^5.4.0", "wait-on": "^8.0.4"}, "packageManager": "pnpm@9.0.6+sha1.648f6014eb363abb36618f2ba59282a9eeb3e879", "build": {"appId": "com.live2d.desktop-pet", "productName": "Live2D 桌面模型", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "public/models/**/*", "public/library/**/*", "public/audio/**/*", "node_modules/**/*"], "extraResources": [{"from": "public/models", "to": "models"}, {"from": "public/library", "to": "library"}, {"from": "public/audio", "to": "audio"}], "mac": {"category": "public.app-category.entertainment", "icon": "public/icon.png", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}]}, "win": {"icon": "public/icon.png", "target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}]}, "linux": {"icon": "public/icon.png", "category": "Game", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Live2D 桌面模型"}}}