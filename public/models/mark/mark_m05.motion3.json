{"Version": 3, "Meta": {"Duration": 4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 173, "TotalPointCount": 487, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 0.11, 1, 0.22, 1, 0.33, 1, 0, 4, 1]}, {"Target": "Model", "Id": "EyeBlink", "Segments": [0, 1, 1, 0.2, 1, 0.4, 1, 0.6, 1, 1, 0.733, 1, 0.867, 0.7, 1, 0.7, 1, 1.222, 0.7, 1.444, 0.7, 1.667, 0.7, 1, 1.711, 0.7, 1.756, 0, 1.8, 0, 1, 1.844, 0, 1.889, -0.001, 1.933, 0.003, 1, 1.978, 0.007, 2.022, 1, 2.067, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 3, 1, 3, 1, 1.211, 3, 1.422, 3, 1.633, 3, 1, 1.733, 3, 1.833, -2, 1.933, -2, 1, 2.222, -2, 2.511, -1.804, 2.8, -1, 1, 2.911, -0.691, 3.022, 0, 3.133, 0, 1, 3.422, 0, 3.711, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.456, 0, 0.544, 24, 0.633, 24, 1, 0.767, 24, 0.9, -17, 1.033, -17, 1, 1.267, -17, 1.5, -17, 1.733, -17, 1, 1.778, -17, 1.822, -29.898, 1.867, -29.898, 1, 1.922, -29.898, 1.978, 13.726, 2.033, 21, 1, 2.089, 28.274, 2.144, 27.545, 2.2, 27.545, 1, 2.433, 27.545, 2.667, -8, 2.9, -8, 1, 3.011, -8, 3.122, 0, 3.233, 0, 1, 3.489, 0, 3.744, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.656, 0, 0.878, -17, 1.1, -17, 1, 1.344, -17, 1.589, -17, 1.833, -17, 1, 1.922, -17, 2.011, 19, 2.1, 19, 1, 2.4, 19, 2.7, -4, 3, -4, 1, 3.111, -4, 3.222, 0, 3.333, 0, 1, 3.556, 0, 3.778, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0.1, 1, 0.1, 1, 1.222, 0.1, 1.444, 0.1, 1.667, 0.1, 1, 1.689, 0.1, 1.711, 0.2, 1.733, 0.2, 1, 1.8, 0.2, 1.867, 0.177, 1.933, 0.1, 1, 1.978, 0.049, 2.022, 0, 2.067, 0, 1, 2.333, 0, 2.6, 0, 2.867, 0, 1, 3.244, 0, 3.622, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, -0.6, 1, -0.6, 1, 1.222, -0.6, 1.444, -0.6, 1.667, -0.6, 1, 1.689, -0.6, 1.711, -1, 1.733, -1, 1, 1.8, -1, 1.867, -1.006, 1.933, -0.9, 1, 1.978, -0.829, 2.022, 0.1, 2.067, 0.1, 1, 2.333, 0.1, 2.6, 0, 2.867, 0, 1, 3.244, 0, 3.622, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.556, 1, 0.778, 0.9, 1, 0.9, 1, 1.233, 0.9, 1.467, 0.9, 1.7, 0.9, 1, 1.744, 0.9, 1.789, 0.7, 1.833, 0.7, 1, 1.889, 0.7, 1.944, 1, 2, 1, 1, 2.667, 1, 3.333, 1, 4, 1]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.556, 1, 0.778, 0.9, 1, 0.9, 1, 1.233, 0.9, 1.467, 0.9, 1.7, 0.9, 1, 1.744, 0.9, 1.789, 0.7, 1.833, 0.7, 1, 1.889, 0.7, 1.944, 1, 2, 1, 1, 2.667, 1, 3.333, 1, 4, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0, 1.833, 0, 1, 1.889, 0, 1.944, 1, 2, 1, 1, 2.067, 1, 2.133, 1, 2.2, 1, 1, 2.422, 1, 2.644, 0, 2.867, 0, 1, 3.244, 0, 3.622, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 3, 1, 3, 1, 1.233, 3, 1.467, 3, 1.7, 3, 1, 1.744, 3, 1.789, 1, 1.833, 1, 1, 1.889, 1, 1.944, 11.968, 2, 15, 1, 2.211, 26.52, 2.422, 30, 2.633, 30, 1, 2.789, 30, 2.944, -1, 3.1, -1, 1, 3.322, -1, 3.544, 0, 3.767, 0, 1, 3.844, 0, 3.922, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 3, 1, 3, 1, 1.233, 3, 1.467, 3, 1.7, 3, 1, 1.744, 3, 1.789, 1, 1.833, 1, 1, 1.889, 1, 1.944, 11.968, 2, 15, 1, 2.211, 26.52, 2.422, 30, 2.633, 30, 1, 2.789, 30, 2.944, -1, 3.1, -1, 1, 3.322, -1, 3.544, 0, 3.767, 0, 1, 3.844, 0, 3.922, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamLeftLeg", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, 0, 2, 0, 1, 2.667, 0, 3.333, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamRightLeg", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, 7.138, 2, 8, 1, 2.211, 9.82, 2.422, 10, 2.633, 10, 1, 2.711, 10, 2.789, 0, 2.867, 0, 1, 3.244, 0, 3.622, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, -4, 2, -4, 1, 2.289, -4, 2.578, 0, 2.867, 0, 1, 3.244, 0, 3.622, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 6, 1, 0.111, 6, 0.222, 6, 0.333, 6, 1, 0.556, 6, 0.778, -30, 1, -30, 1, 1.233, -30, 1.467, -30, 1.7, -30, 1, 1.8, -30, 1.9, 22, 2, 22, 1, 2.289, 22, 2.578, -7, 2.867, -7, 1, 2.978, -7, 3.089, -2.025, 3.2, 0, 1, 3.467, 4.86, 3.733, 6, 4, 6]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.422, 0, 0.511, 13, 0.6, 13, 1, 0.733, 13, 0.867, -14, 1, -14, 1, 1.233, -14, 1.467, -14, 1.7, -14, 1, 1.8, -14, 1.9, 13.084, 2, 18, 1, 2.211, 28.379, 2.422, 30, 2.633, 30, 1, 2.711, 30, 2.789, -13, 2.867, -13, 1, 3.244, -13, 3.622, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, 0, 2, 0, 1, 2.667, 0, 3.333, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, 0, 2, 0, 1, 2.667, 0, 3.333, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, 0, 2, 0, 1, 2.667, 0, 3.333, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.556, 0, 0.778, 0, 1, 0, 1, 1.233, 0, 1.467, 0, 1.7, 0, 1, 1.8, 0, 1.9, 0, 2, 0, 1, 2.667, 0, 3.333, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "PartHead", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartHairFront", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeL", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallL", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteL", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeR", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallR", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteR", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartHairSide", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartMouth", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartOral", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartFace", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartBody", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartArmL", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartArmR", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "PartHairBack", "Segments": [0, 1, 2, 0.33, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 4, 1]}]}